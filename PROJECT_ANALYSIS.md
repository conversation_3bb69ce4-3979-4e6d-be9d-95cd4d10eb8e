# Online Voting System - Project Analysis

## Project Overview
This is a comprehensive Online Voting System built using the MERN stack (MongoDB, Express.js, React.js, Node.js) with advanced features including biometric verification, fraud detection, and real-time updates.

## Architecture

### Frontend (React.js)
- **Port**: 3000
- **Framework**: React 18.2.0 with Create React App
- **UI Libraries**: 
  - Material-UI (@mui/material)
  - Bootstrap 5.3.3
  - React Bootstrap
  - MDB React UI Kit
- **Key Features**:
  - Responsive design
  - Real-time updates with Socket.IO
  - Biometric verification (Face API.js)
  - Charts and data visualization (Chart.js, Nivo)
  - Form validation (Formik, Yup)

### Backend (Node.js/Express)
- **Port**: 5000
- **Database**: MongoDB (with Mongoose ODM)
- **Authentication**: JWT (JSON Web Tokens)
- **File Upload**: Multer
- **Security Features**:
  - CORS enabled
  - Biometric verification
  - Fraud detection
  - ID verification

## Current Status

### ✅ Working Components
1. **Server**: Running on port 5000
2. **Client**: Running on port 3000 with hot reload
3. **Basic Authentication**: Login/Signup functionality
4. **Admin Panel**: Dashboard with statistics
5. **Voter Interface**: User portal and voting interface
6. **File Upload**: Image upload for profiles and IDs

### ⚠️ Issues Identified
1. **Firebase Configuration**: Fixed import issues in firebase.ts
2. **Dependency Conflicts**: Material-UI version conflicts (resolved with --legacy-peer-deps)
3. **ESLint Warnings**: Multiple code quality warnings (non-blocking)

## Key Features

### 1. User Authentication
- **Voter Login**: Email/username + password
- **Admin Login**: Separate admin authentication
- **JWT Token**: Secure session management
- **Default Credentials**:
  - Voter: `<EMAIL>` / `123`
  - Admin: `admin` / `admin@123`

### 2. Voting System
- **Candidate Management**: Add, edit, view candidates
- **Election Management**: Create and manage elections
- **Vote Casting**: Secure voting interface
- **Real-time Results**: Live vote counting

### 3. Admin Dashboard
- **Statistics**: Voter count, candidate count, votes cast
- **Data Visualization**: Charts and graphs
- **User Management**: Voter and candidate management
- **Election Control**: Start/stop elections

### 4. Security Features
- **Biometric Verification**: Face recognition using Face API.js
- **ID Verification**: Document scanning and verification
- **Fraud Detection**: AI-powered fraud detection
- **Multiple Person Detection**: Prevents multiple people voting
- **Voice Detection**: Additional security layer

### 5. Advanced Features
- **Calendar Integration**: Election scheduling
- **Real-time Updates**: WebSocket communication
- **File Management**: Profile pictures, ID proofs, candidate images
- **Responsive Design**: Mobile-friendly interface

## Database Schema

### Voters Collection
```javascript
{
  _id: ObjectId,
  name: String,
  username: String,
  email: String,
  password: String (hashed),
  dob: Date,
  age: Number,
  address: String,
  phone: String,
  idProof: String (filename),
  profilePic: String (filename),
  voteStatus: Boolean
}
```

### Candidates Collection
```javascript
{
  _id: ObjectId,
  firstName: String,
  age: Number,
  party: String,
  bio: String,
  image: String (filename),
  symbol: String (filename),
  votes: Number
}
```

### Elections Collection
```javascript
{
  _id: ObjectId,
  title: String,
  description: String,
  startDate: Date,
  endDate: Date,
  status: String,
  candidates: [ObjectId]
}
```

### Admins Collection
```javascript
{
  _id: ObjectId,
  username: String,
  password: String (hashed)
}
```

## API Endpoints

### Authentication
- `POST /login` - Voter login
- `POST /adminlogin` - Admin login

### Candidates
- `GET /getCandidate` - Get all candidates
- `PATCH /getCandidate/:id` - Update candidate votes

### Voters
- `PATCH /updateVoter/:id` - Update voter status

### Dashboard
- `GET /getDashboardData` - Get dashboard statistics

## File Structure
```
Online-Voting-System-main/
├── src/
│   ├── components/
│   │   ├── Home/           # Landing page components
│   │   ├── Sign/           # Authentication components
│   │   ├── User/           # Voter interface
│   │   ├── NewDashboard/   # Admin dashboard
│   │   └── Navbar/         # Navigation component
│   ├── utils/              # Utility functions
│   └── helper.js           # Configuration constants
├── server/
│   ├── routes/             # API routes
│   ├── models/             # Database models
│   ├── middleware/         # Custom middleware
│   ├── biometrics/         # Biometric verification
│   ├── ai/                 # AI fraud detection
│   └── uploads/            # File uploads
└── public/                 # Static assets
```

## Technologies Used

### Frontend Dependencies
- React 18.2.0
- Material-UI 5.x
- Bootstrap 5.3.3
- Chart.js 4.4.2
- Face API.js 0.22.2
- Socket.IO Client 4.7.5
- Axios 1.6.8
- React Router DOM 6.22.3

### Backend Dependencies
- Express 4.18.2
- Mongoose 8.0.0
- JWT 9.0.2
- Multer 1.4.5
- Face API.js 0.22.2
- TensorFlow.js Node 4.10.0
- Winston (logging)
- Canvas 2.11.2

## Getting Started

### Prerequisites
- Node.js 14+
- MongoDB
- Modern web browser with camera support

### Installation Steps
1. Clone the repository
2. Install client dependencies: `npm install --legacy-peer-deps`
3. Install server dependencies: `cd server && npm install`
4. Start the server: `npm start` (from server directory)
5. Start the client: `npm start` (from root directory)

### Default Access
- **Application**: http://localhost:3000
- **Server API**: http://localhost:5000
- **Voter Login**: <EMAIL> / 123
- **Admin Login**: admin / admin@123

## Sample Data Available
The system includes pre-configured sample data:
- 1 Test voter account
- 3 Sample candidates
- 1 Admin account
- 1 Sample election

## Next Steps for Enhancement
1. Fix ESLint warnings for better code quality
2. Implement proper MongoDB connection
3. Add more comprehensive error handling
4. Enhance biometric verification
5. Add more test data
6. Implement proper logging
7. Add unit tests
8. Enhance security measures
