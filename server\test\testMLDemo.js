/**
 * Demo Test for ML Document Classification System
 * 
 * This test demonstrates the ML system functionality using the demo version
 * that doesn't require TensorFlow.js dependencies.
 */

const documentClassification = require('../ai/documentClassificationDemo');

/**
 * Create a simple test image buffer (placeholder)
 */
function createTestImageBuffer() {
  // Create a simple 100x100 RGB image buffer
  const width = 100;
  const height = 100;
  const channels = 3;
  const buffer = Buffer.alloc(width * height * channels);
  
  // Fill with random pixel data
  for (let i = 0; i < buffer.length; i++) {
    buffer[i] = Math.floor(Math.random() * 256);
  }
  
  return buffer;
}

/**
 * Demo test for document classification
 */
async function demoDocumentClassification() {
  console.log('\n🎯 === Demo: Document Classification ===');
  
  try {
    // Initialize system
    console.log('Initializing system...');
    const initResult = await documentClassification.initializeDocumentClassification();
    
    if (!initResult) {
      console.log('❌ Failed to initialize system');
      return false;
    }
    
    // Test classification
    console.log('Testing document classification...');
    const testImageBuffer = createTestImageBuffer();
    const result = await documentClassification.classifyDocument(testImageBuffer);
    
    if (result.success) {
      console.log('✅ Classification successful!');
      console.log(`📄 Document Type: ${result.documentType.toUpperCase()}`);
      console.log(`🎯 Confidence: ${(result.confidence * 100).toFixed(1)}%`);
      console.log(`⏱️  Processing Time: ${result.processingTime}ms`);
      
      // Show top 3 predictions
      const sortedProbs = Object.entries(result.probabilities)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3);
      
      console.log('🏆 Top 3 Predictions:');
      sortedProbs.forEach(([type, prob], index) => {
        console.log(`   ${index + 1}. ${type.toUpperCase()}: ${(prob * 100).toFixed(1)}%`);
      });
      
      return true;
    } else {
      console.log('❌ Classification failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Error in demo classification:', error.message);
    return false;
  }
}

/**
 * Demo test for feature extraction
 */
async function demoFeatureExtraction() {
  console.log('\n🔍 === Demo: Feature Extraction ===');
  
  try {
    const testImageBuffer = createTestImageBuffer();
    const result = await documentClassification.extractDocumentFeatures(testImageBuffer);
    
    if (result.success) {
      console.log('✅ Feature extraction successful!');
      console.log(`🧠 Visual Features: ${result.visualFeatures.length} dimensions`);
      console.log('📝 Text Features Detected:');
      Object.entries(result.textFeatures).forEach(([feature, detected]) => {
        const status = detected ? '✅' : '❌';
        console.log(`   ${status} ${feature}`);
      });
      
      console.log('📐 Layout Features:');
      console.log(`   📏 Dimensions: ${result.layoutFeatures.width}x${result.layoutFeatures.height}`);
      console.log(`   📊 Aspect Ratio: ${result.layoutFeatures.aspectRatio.toFixed(2)}`);
      console.log(`   💡 Brightness: ${result.layoutFeatures.brightness.toFixed(1)}`);
      console.log(`   🔳 Edge Count: ${result.layoutFeatures.edgeCount}`);
      console.log(`⏱️  Processing Time: ${result.processingTime}ms`);
      
      return true;
    } else {
      console.log('❌ Feature extraction failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Error in demo feature extraction:', error.message);
    return false;
  }
}

/**
 * Demo test for document validation
 */
async function demoDocumentValidation() {
  console.log('\n🛡️  === Demo: Document Validation ===');
  
  try {
    const testImageBuffer = createTestImageBuffer();
    const result = await documentClassification.validateDocumentAuthenticity(testImageBuffer);
    
    if (result.success) {
      console.log('✅ Document validation successful!');
      
      const authenticIcon = result.isAuthentic ? '✅' : '⚠️';
      console.log(`${authenticIcon} Document Authenticity: ${result.isAuthentic ? 'AUTHENTIC' : 'SUSPICIOUS'}`);
      console.log(`📊 Authenticity Score: ${(result.authenticity_score * 100).toFixed(1)}%`);
      
      if (result.risk_factors && result.risk_factors.length > 0) {
        console.log('⚠️  Risk Factors Detected:');
        result.risk_factors.forEach((factor, index) => {
          console.log(`   ${index + 1}. ${factor}`);
        });
      } else {
        console.log('✅ No risk factors detected');
      }
      
      console.log(`⏱️  Processing Time: ${result.processingTime}ms`);
      
      return true;
    } else {
      console.log('❌ Document validation failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Error in demo validation:', error.message);
    return false;
  }
}

/**
 * Demo test for training simulation
 */
async function demoTrainingSimulation() {
  console.log('\n🎓 === Demo: Training Simulation ===');
  
  try {
    // Create mock training data
    const mockTrainingFiles = [
      { filename: 'aadhar_001.jpg', documentType: 'aadhar' },
      { filename: 'aadhar_002.jpg', documentType: 'aadhar' },
      { filename: 'passport_001.jpg', documentType: 'passport' },
      { filename: 'voter_id_001.jpg', documentType: 'voter_id' },
      { filename: 'pan_card_001.jpg', documentType: 'pan_card' }
    ];
    
    console.log(`🗂️  Training with ${mockTrainingFiles.length} sample files`);
    
    // Progress callback to show training progress
    let currentEpoch = 0;
    const progressCallback = (epoch, metrics) => {
      currentEpoch = epoch;
      if (epoch % 10 === 0 || epoch < 5) {
        console.log(`   Epoch ${epoch + 1}: Loss=${metrics.loss.toFixed(3)}, Accuracy=${(metrics.acc * 100).toFixed(1)}%`);
      }
    };
    
    const trainingOptions = {
      epochs: 20, // Reduced for demo
      progressCallback: progressCallback
    };
    
    const result = await documentClassification.trainDocumentClassificationModel(
      mockTrainingFiles, 
      trainingOptions
    );
    
    if (result.success) {
      console.log('✅ Training simulation completed!');
      console.log(`🎯 Final Accuracy: ${(result.finalAccuracy * 100).toFixed(1)}%`);
      console.log(`📉 Final Loss: ${result.finalLoss.toFixed(3)}`);
      console.log(`⏱️  Training Time: ${result.trainingTime}ms`);
      
      return true;
    } else {
      console.log('❌ Training simulation failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Error in demo training:', error.message);
    return false;
  }
}

/**
 * Demo test for system information
 */
function demoSystemInfo() {
  console.log('\n📋 === Demo: System Information ===');
  
  try {
    const modelInfo = documentClassification.getModelInfo();
    const isReady = documentClassification.isSystemReady();
    const documentTypes = documentClassification.DOCUMENT_TYPES;
    
    console.log('✅ System information retrieved!');
    console.log(`🤖 Model Version: ${modelInfo.version}`);
    console.log(`🧠 Model Type: ${modelInfo.modelType}`);
    console.log(`📐 Input Shape: [${modelInfo.inputShape.join(', ')}]`);
    console.log(`📊 Output Classes: ${modelInfo.outputClasses}`);
    console.log(`🔄 System Ready: ${isReady ? 'Yes' : 'No'}`);
    
    console.log('📄 Supported Document Types:');
    Object.entries(documentTypes).forEach(([key, value]) => {
      console.log(`   • ${key}: ${value}`);
    });
    
    return true;
  } catch (error) {
    console.error('❌ Error getting system info:', error.message);
    return false;
  }
}

/**
 * Run all demo tests
 */
async function runDemoTests() {
  console.log('🚀 ML Document Classification System - DEMO MODE');
  console.log('================================================');
  console.log('💡 This demo shows system functionality without requiring TensorFlow.js');
  console.log('🔧 For full functionality, install: npm install @tensorflow/tfjs');
  console.log('');
  
  const testResults = {
    systemInfo: false,
    classification: false,
    featureExtraction: false,
    validation: false,
    training: false
  };
  
  // Run demo tests
  testResults.systemInfo = demoSystemInfo();
  testResults.classification = await demoDocumentClassification();
  testResults.featureExtraction = await demoFeatureExtraction();
  testResults.validation = await demoDocumentValidation();
  testResults.training = await demoTrainingSimulation();
  
  // Summary
  console.log('\n📊 === Demo Test Results Summary ===');
  const passedTests = Object.values(testResults).filter(result => result).length;
  const totalTests = Object.keys(testResults).length;
  
  Object.entries(testResults).forEach(([testName, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${testName}`);
  });
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} demo tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All demo tests passed! The ML system structure is working correctly.');
    console.log('');
    console.log('🚀 Next Steps:');
    console.log('   1. Install TensorFlow.js: npm install @tensorflow/tfjs');
    console.log('   2. Replace demo module with full implementation');
    console.log('   3. Start the server and test the web interface');
    console.log('   4. Upload real document images for training');
  } else {
    console.log('⚠️  Some demo tests failed. Please check the implementation.');
  }
  
  return passedTests === totalTests;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runDemoTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Demo test execution failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runDemoTests,
  demoDocumentClassification,
  demoFeatureExtraction,
  demoDocumentValidation,
  demoTrainingSimulation,
  demoSystemInfo
};
