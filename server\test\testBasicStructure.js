/**
 * Basic Structure Test for ML Document System
 * 
 * This test verifies that all the files and modules are properly structured
 * without requiring heavy dependencies like TensorFlow.js
 */

const path = require('path');
const fs = require('fs');

// Test configuration
const TEST_PATHS = {
  aiModules: [
    '../ai/documentClassification.js',
    '../ai/trainingManager.js'
  ],
  routes: [
    '../routes/mlDocumentRoutes.js'
  ],
  frontendComponents: [
    '../../src/components/MLDocumentTraining.js',
    '../../src/components/DocumentClassifier.js'
  ],
  directories: [
    '../models',
    '../training_data',
    '../logs',
    '../uploads'
  ]
};

/**
 * Test if files exist
 */
function testFileExistence() {
  console.log('\n=== Testing File Existence ===');
  
  let allFilesExist = true;
  
  // Test AI modules
  console.log('Checking AI modules...');
  TEST_PATHS.aiModules.forEach(filePath => {
    const fullPath = path.join(__dirname, filePath);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${filePath}`);
    } else {
      console.log(`❌ ${filePath} - NOT FOUND`);
      allFilesExist = false;
    }
  });
  
  // Test routes
  console.log('Checking routes...');
  TEST_PATHS.routes.forEach(filePath => {
    const fullPath = path.join(__dirname, filePath);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${filePath}`);
    } else {
      console.log(`❌ ${filePath} - NOT FOUND`);
      allFilesExist = false;
    }
  });
  
  // Test frontend components
  console.log('Checking frontend components...');
  TEST_PATHS.frontendComponents.forEach(filePath => {
    const fullPath = path.join(__dirname, filePath);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${filePath}`);
    } else {
      console.log(`❌ ${filePath} - NOT FOUND`);
      allFilesExist = false;
    }
  });
  
  return allFilesExist;
}

/**
 * Test directory structure
 */
function testDirectoryStructure() {
  console.log('\n=== Testing Directory Structure ===');
  
  let allDirsExist = true;
  
  TEST_PATHS.directories.forEach(dirPath => {
    const fullPath = path.join(__dirname, dirPath);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${dirPath}/`);
    } else {
      console.log(`⚠️  ${dirPath}/ - Creating directory...`);
      try {
        fs.mkdirSync(fullPath, { recursive: true });
        console.log(`✅ ${dirPath}/ - Created successfully`);
      } catch (error) {
        console.log(`❌ ${dirPath}/ - Failed to create: ${error.message}`);
        allDirsExist = false;
      }
    }
  });
  
  return allDirsExist;
}

/**
 * Test module exports (basic syntax check)
 */
function testModuleExports() {
  console.log('\n=== Testing Module Exports ===');
  
  let allModulesValid = true;
  
  // Test if modules can be required (syntax check)
  const modulesToTest = [
    { name: 'trainingManager', path: '../ai/trainingManager.js' },
    { name: 'mlDocumentRoutes', path: '../routes/mlDocumentRoutes.js' }
  ];
  
  modulesToTest.forEach(({ name, path: modulePath }) => {
    try {
      const fullPath = path.join(__dirname, modulePath);
      if (fs.existsSync(fullPath)) {
        // Just check if the file can be read and has basic structure
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // Check for basic module structure
        if (content.includes('module.exports') || content.includes('exports.')) {
          console.log(`✅ ${name} - Module structure valid`);
        } else {
          console.log(`⚠️  ${name} - No exports found`);
        }
        
        // Check for function definitions
        const functionCount = (content.match(/function\s+\w+/g) || []).length;
        const arrowFunctionCount = (content.match(/\w+\s*=\s*async?\s*\(/g) || []).length;
        console.log(`   Functions found: ${functionCount + arrowFunctionCount}`);
        
      } else {
        console.log(`❌ ${name} - File not found`);
        allModulesValid = false;
      }
    } catch (error) {
      console.log(`❌ ${name} - Error reading module: ${error.message}`);
      allModulesValid = false;
    }
  });
  
  return allModulesValid;
}

/**
 * Test package.json dependencies
 */
function testPackageDependencies() {
  console.log('\n=== Testing Package Dependencies ===');
  
  try {
    const packagePath = path.join(__dirname, '../package.json');
    if (!fs.existsSync(packagePath)) {
      console.log('❌ package.json not found');
      return false;
    }
    
    const packageContent = fs.readFileSync(packagePath, 'utf8');
    const packageJson = JSON.parse(packageContent);
    
    const requiredDeps = [
      '@tensorflow/tfjs-node',
      'express',
      'multer',
      'canvas',
      'node-tesseract-ocr'
    ];
    
    let allDepsPresent = true;
    
    console.log('Checking required dependencies...');
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        console.log(`✅ ${dep} - ${packageJson.dependencies[dep]}`);
      } else {
        console.log(`❌ ${dep} - NOT FOUND`);
        allDepsPresent = false;
      }
    });
    
    return allDepsPresent;
  } catch (error) {
    console.log(`❌ Error reading package.json: ${error.message}`);
    return false;
  }
}

/**
 * Test API route structure
 */
function testAPIRouteStructure() {
  console.log('\n=== Testing API Route Structure ===');
  
  try {
    const routePath = path.join(__dirname, '../routes/mlDocumentRoutes.js');
    if (!fs.existsSync(routePath)) {
      console.log('❌ ML document routes file not found');
      return false;
    }
    
    const routeContent = fs.readFileSync(routePath, 'utf8');
    
    // Check for expected route patterns
    const expectedRoutes = [
      '/classify',
      '/training/session/init',
      '/training/samples/upload',
      '/training/start',
      '/training/history',
      '/document-types'
    ];
    
    let allRoutesFound = true;
    
    console.log('Checking API routes...');
    expectedRoutes.forEach(route => {
      if (routeContent.includes(route)) {
        console.log(`✅ ${route}`);
      } else {
        console.log(`❌ ${route} - NOT FOUND`);
        allRoutesFound = false;
      }
    });
    
    // Check for HTTP methods
    const httpMethods = ['GET', 'POST', 'DELETE'];
    httpMethods.forEach(method => {
      const methodPattern = new RegExp(`router\\.${method.toLowerCase()}`, 'g');
      const matches = routeContent.match(methodPattern);
      if (matches) {
        console.log(`✅ ${method} routes: ${matches.length} found`);
      }
    });
    
    return allRoutesFound;
  } catch (error) {
    console.log(`❌ Error reading routes file: ${error.message}`);
    return false;
  }
}

/**
 * Run all basic tests
 */
function runBasicTests() {
  console.log('🚀 Starting Basic Structure Tests for ML Document System');
  console.log('=======================================================');
  
  const testResults = {
    fileExistence: false,
    directoryStructure: false,
    moduleExports: false,
    packageDependencies: false,
    apiRouteStructure: false
  };
  
  // Run tests
  testResults.fileExistence = testFileExistence();
  testResults.directoryStructure = testDirectoryStructure();
  testResults.moduleExports = testModuleExports();
  testResults.packageDependencies = testPackageDependencies();
  testResults.apiRouteStructure = testAPIRouteStructure();
  
  // Summary
  console.log('\n=== Basic Test Results Summary ===');
  const passedTests = Object.values(testResults).filter(result => result).length;
  const totalTests = Object.keys(testResults).length;
  
  Object.entries(testResults).forEach(([testName, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${testName}`);
  });
  
  console.log(`\n📊 Overall: ${passedTests}/${totalTests} basic tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All basic structure tests passed!');
    console.log('💡 Note: To test ML functionality, install missing dependencies:');
    console.log('   npm install @tensorflow/tfjs');
  } else {
    console.log('⚠️  Some basic structure tests failed. Please check the implementation.');
  }
  
  return passedTests === totalTests;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runBasicTests()
    .then ? runBasicTests().then(success => {
      process.exit(success ? 0 : 1);
    }).catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    }) : (() => {
      const success = runBasicTests();
      process.exit(success ? 0 : 1);
    })();
}

module.exports = {
  runBasicTests,
  testFileExistence,
  testDirectoryStructure,
  testModuleExports,
  testPackageDependencies,
  testAPIRouteStructure
};
