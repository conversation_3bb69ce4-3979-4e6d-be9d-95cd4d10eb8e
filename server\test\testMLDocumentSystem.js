/**
 * Test Script for ML Document Classification System
 * 
 * This script tests the core functionality of the document classification system
 * including model initialization, document classification, and training capabilities.
 */

const path = require('path');
const fs = require('fs');

// Import the ML modules (with error handling for missing dependencies)
let documentClassification, trainingManager;
try {
  documentClassification = require('../ai/documentClassification');
  trainingManager = require('../ai/trainingManager');
} catch (error) {
  console.log('⚠️  Some dependencies are missing. Running limited tests...');
  console.log('Error:', error.message);
}

// Test configuration
const TEST_CONFIG = {
  testImagePath: path.join(__dirname, 'test_images'),
  outputPath: path.join(__dirname, 'test_results'),
  sampleDocuments: [
    { name: 'sample_aadhar.jpg', type: 'aadhar' },
    { name: 'sample_voter_id.jpg', type: 'voter_id' },
    { name: 'sample_passport.jpg', type: 'passport' }
  ]
};

// Ensure test directories exist
[TEST_CONFIG.testImagePath, TEST_CONFIG.outputPath].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

/**
 * Create a simple test image buffer (placeholder)
 * In a real test, you would use actual document images
 */
function createTestImageBuffer() {
  // Create a simple 100x100 RGB image buffer
  const width = 100;
  const height = 100;
  const channels = 3;
  const buffer = Buffer.alloc(width * height * channels);
  
  // Fill with random pixel data
  for (let i = 0; i < buffer.length; i++) {
    buffer[i] = Math.floor(Math.random() * 256);
  }
  
  return buffer;
}

/**
 * Test document classification initialization
 */
async function testInitialization() {
  console.log('\n=== Testing ML Document System Initialization ===');

  if (!documentClassification) {
    console.log('⚠️  Skipping initialization test - dependencies not available');
    return false;
  }

  try {
    const result = await documentClassification.initializeDocumentClassification();

    if (result) {
      console.log('✅ Document classification system initialized successfully');
      return true;
    } else {
      console.log('❌ Document classification system initialization failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Error during initialization:', error.message);
    return false;
  }
}

/**
 * Test document classification
 */
async function testDocumentClassification() {
  console.log('\n=== Testing Document Classification ===');

  if (!documentClassification) {
    console.log('⚠️  Skipping classification test - dependencies not available');
    return false;
  }

  try {
    // Create test image buffer
    const testImageBuffer = createTestImageBuffer();
    
    console.log('Testing document classification...');
    const classificationResult = await documentClassification.classifyDocument(testImageBuffer);
    
    if (classificationResult.success) {
      console.log('✅ Document classification successful');
      console.log(`   Document Type: ${classificationResult.documentType}`);
      console.log(`   Confidence: ${(classificationResult.confidence * 100).toFixed(2)}%`);
      console.log(`   Processing Time: ${classificationResult.processingTime}ms`);
      
      // Display top 3 probabilities
      const sortedProbs = Object.entries(classificationResult.probabilities)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3);
      
      console.log('   Top 3 Predictions:');
      sortedProbs.forEach(([type, prob], index) => {
        console.log(`     ${index + 1}. ${type}: ${(prob * 100).toFixed(2)}%`);
      });
      
      return true;
    } else {
      console.log('❌ Document classification failed:', classificationResult.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Error during document classification:', error.message);
    return false;
  }
}

/**
 * Test feature extraction
 */
async function testFeatureExtraction() {
  console.log('\n=== Testing Feature Extraction ===');
  
  try {
    const testImageBuffer = createTestImageBuffer();
    
    console.log('Testing feature extraction...');
    const featuresResult = await documentClassification.extractDocumentFeatures(testImageBuffer);
    
    if (featuresResult.success) {
      console.log('✅ Feature extraction successful');
      console.log(`   Visual Features Shape: ${featuresResult.visualFeatures ? featuresResult.visualFeatures.length : 'N/A'}`);
      console.log(`   Text Features Detected: ${featuresResult.textFeatures ? Object.keys(featuresResult.textFeatures).length : 'N/A'}`);
      console.log(`   Layout Features: ${featuresResult.layoutFeatures ? 'Available' : 'N/A'}`);
      console.log(`   Processing Time: ${featuresResult.processingTime}ms`);
      
      return true;
    } else {
      console.log('❌ Feature extraction failed:', featuresResult.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Error during feature extraction:', error.message);
    return false;
  }
}

/**
 * Test document authenticity validation
 */
async function testDocumentValidation() {
  console.log('\n=== Testing Document Validation ===');
  
  try {
    const testImageBuffer = createTestImageBuffer();
    
    console.log('Testing document validation...');
    const validationResult = await documentClassification.validateDocumentAuthenticity(testImageBuffer);
    
    if (validationResult.success) {
      console.log('✅ Document validation successful');
      console.log(`   Is Authentic: ${validationResult.isAuthentic ? 'Yes' : 'No'}`);
      console.log(`   Authenticity Score: ${(validationResult.authenticity_score * 100).toFixed(2)}%`);
      console.log(`   Risk Factors: ${validationResult.risk_factors ? validationResult.risk_factors.length : 0}`);
      console.log(`   Processing Time: ${validationResult.processingTime}ms`);
      
      if (validationResult.risk_factors && validationResult.risk_factors.length > 0) {
        console.log('   Risk Factors:');
        validationResult.risk_factors.forEach((factor, index) => {
          console.log(`     ${index + 1}. ${factor}`);
        });
      }
      
      return true;
    } else {
      console.log('❌ Document validation failed:', validationResult.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Error during document validation:', error.message);
    return false;
  }
}

/**
 * Test training manager functionality
 */
async function testTrainingManager() {
  console.log('\n=== Testing Training Manager ===');
  
  try {
    // Initialize training session
    console.log('Initializing training session...');
    const session = trainingManager.initializeTrainingSession({
      epochs: 5,
      batchSize: 16,
      learningRate: 0.001
    });
    
    if (session) {
      console.log('✅ Training session initialized');
      console.log(`   Session ID: ${session.id}`);
      console.log(`   Configuration: ${JSON.stringify(session.config, null, 2)}`);
    } else {
      console.log('❌ Training session initialization failed');
      return false;
    }
    
    // Test session stats
    console.log('Getting session stats...');
    const stats = trainingManager.getTrainingSessionStats();
    
    if (stats && !stats.error) {
      console.log('✅ Session stats retrieved');
      console.log(`   Status: ${stats.status}`);
      console.log(`   Training Samples: ${stats.datasetStats.training}`);
    } else {
      console.log('❌ Failed to get session stats:', stats.error);
    }
    
    // Test training history
    console.log('Getting training history...');
    const history = trainingManager.getTrainingHistory();
    console.log(`✅ Training history retrieved (${history.length} sessions)`);
    
    // Clear session
    trainingManager.clearCurrentSession();
    console.log('✅ Training session cleared');
    
    return true;
  } catch (error) {
    console.error('❌ Error during training manager test:', error.message);
    return false;
  }
}

/**
 * Test document types configuration
 */
function testDocumentTypes() {
  console.log('\n=== Testing Document Types Configuration ===');
  
  try {
    const documentTypes = documentClassification.DOCUMENT_TYPES;
    
    console.log('✅ Document types loaded');
    console.log('   Supported document types:');
    Object.entries(documentTypes).forEach(([key, value]) => {
      console.log(`     ${key}: ${value}`);
    });
    
    // Verify all required types are present
    const requiredTypes = ['AADHAR', 'VOTER_ID', 'DRIVING_LICENSE', 'PASSPORT', 'PAN_CARD'];
    const missingTypes = requiredTypes.filter(type => !documentTypes[type]);
    
    if (missingTypes.length === 0) {
      console.log('✅ All required document types are configured');
      return true;
    } else {
      console.log('❌ Missing document types:', missingTypes);
      return false;
    }
  } catch (error) {
    console.error('❌ Error testing document types:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting ML Document Classification System Tests');
  console.log('================================================');
  
  const testResults = {
    initialization: false,
    documentTypes: false,
    classification: false,
    featureExtraction: false,
    validation: false,
    trainingManager: false
  };
  
  // Run tests
  testResults.documentTypes = testDocumentTypes();
  testResults.initialization = await testInitialization();
  
  if (testResults.initialization) {
    testResults.classification = await testDocumentClassification();
    testResults.featureExtraction = await testFeatureExtraction();
    testResults.validation = await testDocumentValidation();
  }
  
  testResults.trainingManager = await testTrainingManager();
  
  // Summary
  console.log('\n=== Test Results Summary ===');
  const passedTests = Object.values(testResults).filter(result => result).length;
  const totalTests = Object.keys(testResults).length;
  
  Object.entries(testResults).forEach(([testName, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${testName}`);
  });
  
  console.log(`\n📊 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! ML Document Classification System is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
  
  return passedTests === totalTests;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testInitialization,
  testDocumentClassification,
  testFeatureExtraction,
  testDocumentValidation,
  testTrainingManager,
  testDocumentTypes
};
