# Sample Data for Online Voting System

## Test Accounts

### Voter Accounts
```javascript
// Default voter account (already configured)
{
  email: "<EMAIL>",
  username: "testuser", 
  password: "123",
  name: "Test User",
  age: 33,
  voteStatus: false
}

// Additional test voters you can add:
{
  email: "<EMAIL>",
  username: "johns<PERSON>",
  password: "123",
  name: "<PERSON>",
  age: 28,
  address: "456 Oak Street, Springfield",
  phone: "**********",
  voteStatus: false
}

{
  email: "<EMAIL>", 
  username: "sarah<PERSON>",
  password: "123",
  name: "<PERSON>",
  age: 35,
  address: "789 Pine Avenue, Riverside",
  phone: "**********",
  voteStatus: false
}

{
  email: "<EMAIL>",
  username: "miked", 
  password: "123",
  name: "<PERSON>",
  age: 42,
  address: "321 Elm Drive, Lakeside",
  phone: "**********",
  voteStatus: true
}
```

### Admin Account
```javascript
{
  username: "admin",
  password: "admin@123"
}
```

## Sample Candidates

### Presidential Election Candidates
```javascript
[
  {
    firstName: "<PERSON>",
    fullName: "<PERSON>e",
    age: 45,
    party: "Democratic Party", 
    bio: "Experienced leader with a focus on education and healthcare. Former governor with 15 years of public service.",
    image: "john-doe.jpg",
    symbol: "donkey-symbol.jpg",
    votes: 10,
    policies: [
      "Universal Healthcare",
      "Education Reform", 
      "Climate Action",
      "Economic Recovery"
    ]
  },
  {
    firstName: "Jane <PERSON>",
    fullName: "Jane Smith", 
    age: 50,
    party: "Republican Party",
    bio: "Business leader with a strong economic policy background. CEO of major corporation for 10 years.",
    image: "jane-smith.jpg", 
    symbol: "elephant-symbol.jpg",
    votes: 8,
    policies: [
      "Tax Reduction",
      "Business Growth",
      "Infrastructure Development", 
      "National Security"
    ]
  },
  {
    firstName: "Alex Johnson",
    fullName: "Alex Johnson",
    age: 38, 
    party: "Independent",
    bio: "Community organizer focused on social justice and environmental issues. Grassroots activist for 12 years.",
    image: "alex-johnson.jpg",
    symbol: "star-symbol.jpg", 
    votes: 5,
    policies: [
      "Social Justice Reform",
      "Environmental Protection",
      "Community Development",
      "Government Transparency"
    ]
  },
  {
    firstName: "Maria Rodriguez",
    fullName: "Maria Rodriguez",
    age: 43,
    party: "Green Party", 
    bio: "Environmental scientist and policy expert. Leading advocate for sustainable development.",
    image: "maria-rodriguez.jpg",
    symbol: "leaf-symbol.jpg",
    votes: 3,
    policies: [
      "Renewable Energy",
      "Climate Emergency Action",
      "Sustainable Agriculture", 
      "Green Jobs Creation"
    ]
  }
]
```

## Sample Elections

### Current Election
```javascript
{
  title: "Presidential Election 2024",
  description: "National election to choose the next President of the United States",
  startDate: "2024-11-01T08:00:00Z",
  endDate: "2024-11-01T20:00:00Z", 
  status: "active",
  candidates: ["1", "2", "3", "4"],
  totalVotes: 26,
  eligibleVoters: 150000000
}
```

### Upcoming Elections
```javascript
[
  {
    title: "Senate Elections 2024",
    description: "State senate representative elections",
    startDate: "2024-11-05T08:00:00Z", 
    endDate: "2024-11-05T20:00:00Z",
    status: "upcoming",
    candidates: ["5", "6", "7"],
    eligibleVoters: 5000000
  },
  {
    title: "Local Mayor Election",
    description: "City mayor election for Springfield", 
    startDate: "2024-12-01T08:00:00Z",
    endDate: "2024-12-01T18:00:00Z",
    status: "upcoming",
    candidates: ["8", "9"],
    eligibleVoters: 250000
  }
]
```

## Dashboard Statistics

### Sample Dashboard Data
```javascript
{
  voterCount: 150000000,
  candidateCount: 4,
  votersVoted: 26,
  totalElections: 3,
  activeElections: 1,
  upcomingElections: 2,
  completedElections: 15,
  voterTurnout: "0.000017%", // Very low for demo purposes
  topCandidate: {
    name: "John Doe",
    votes: 10,
    percentage: "38.5%"
  },
  recentActivity: [
    {
      type: "vote_cast",
      voter: "Mike Davis", 
      timestamp: "2024-07-23T18:30:00Z",
      candidate: "John Doe"
    },
    {
      type: "voter_registered",
      voter: "Sarah Johnson",
      timestamp: "2024-07-23T17:45:00Z"
    },
    {
      type: "candidate_added", 
      candidate: "Maria Rodriguez",
      timestamp: "2024-07-23T16:20:00Z"
    }
  ]
}
```

## Test Scenarios

### Voting Flow Test
1. **Login as voter**: <EMAIL> / 123
2. **View candidates**: See all 4 candidates with their details
3. **Cast vote**: Select a candidate and submit vote
4. **Verify vote status**: Check that voteStatus becomes true
5. **Prevent double voting**: Try to vote again (should be blocked)

### Admin Flow Test  
1. **Admin login**: admin / admin@123
2. **View dashboard**: See statistics and charts
3. **Manage candidates**: Add/edit candidate information
4. **Manage voters**: View voter list and status
5. **Monitor results**: Real-time vote counting

### Security Test Scenarios
1. **Biometric verification**: Test face recognition during voting
2. **ID verification**: Upload and verify identity documents  
3. **Fraud detection**: Multiple person detection
4. **Session management**: JWT token expiration handling

## API Testing Data

### Login Request
```json
POST /login
{
  "username": "<EMAIL>",
  "password": "123"
}
```

### Vote Request  
```json
PATCH /getCandidate/1
{
  "voterId": "1"
}
```

### Add Candidate Request
```json
POST /addCandidate
{
  "firstName": "Robert Wilson",
  "age": 52,
  "party": "Libertarian Party",
  "bio": "Constitutional lawyer and civil rights advocate",
  "policies": ["Limited Government", "Individual Liberty", "Free Markets"]
}
```

## File Upload Test Data

### Required Upload Directories
- `/server/uploads/profiles/` - Voter profile pictures
- `/server/uploads/idProofs/` - Identity verification documents  
- `/server/uploads/candidates/` - Candidate photos
- `/server/uploads/symbols/` - Party symbols

### Sample File Names
- `john-doe-profile.jpg`
- `voter-id-123.jpg` 
- `democratic-symbol.png`
- `candidate-bio-photo.jpg`

## Database Seeding Commands

To populate the database with sample data:

```bash
# From server directory
npm run seed
```

This will create:
- 4 sample voters
- 4 sample candidates  
- 1 admin account
- 2 sample elections
- Upload directory structure

## Performance Test Data

### Load Testing Scenarios
- **Concurrent voters**: 100 simultaneous login attempts
- **Vote casting**: 50 votes cast within 1 minute
- **Dashboard refresh**: 20 admin dashboard refreshes per second
- **File uploads**: 10 concurrent profile picture uploads

### Expected Response Times
- Login: < 500ms
- Vote casting: < 1000ms  
- Dashboard load: < 2000ms
- File upload: < 5000ms

## Security Test Cases

### Authentication Tests
- Invalid credentials
- SQL injection attempts
- JWT token manipulation
- Session timeout handling

### Authorization Tests  
- Voter accessing admin routes
- Admin accessing voter-specific data
- Unauthorized file access
- Cross-user data access

### Input Validation Tests
- XSS attack prevention
- File upload restrictions
- Form input sanitization
- API parameter validation
